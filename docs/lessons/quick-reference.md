# Quick Reference - Coverage Problem Solving

## 1. IMMEDIATE DIAGNOSTIC
```bash
npm test -- --coverage | grep "Uncovered Line"
```

## 2. PATTERN MATCHING
- **Lines 200-230** → Constructor pattern → jest.doMock + dynamic import
- **Lines 290-300** → Setup pattern → call-count mock pattern  
- **Lines 550-600** → Processing pattern → mock processing dependencies
- **Lines 900+** → Runtime pattern → natural error conditions

## 3. TEMPLATE SELECTION
Use: `./docs/lessons/templates/catch-block-coverage.template.ts`

## 4. AI PROMPT TEMPLATE
"Coverage gaps: [LINES]
Apply pattern from ./docs/lessons/testing-patterns/jest-mocking-patterns.md
Use template from ./docs/lessons/templates/catch-block-coverage.template.ts
Target: 100% coverage in 15-30 minutes"

## 5. SUCCESS VERIFICATION
✅ Lines covered
✅ Time under 30 minutes
✅ No test regressions
✅ Fallback behavior verified

---

# Quick Reference - Branch Coverage (After Line Coverage Success)

## 1. BRANCH COVERAGE DIAGNOSTIC
```bash
npm test -- --coverage | grep -E "Branch|Line"
```

## 2. BRANCH PATTERN MATCHING
- **100% Line + <100% Branch** → Dual Path Pattern → Test both success AND failure branches
- **Complex conditionals** → Multi-Conditional Pattern → Test all logical combinations
- **Missing success branch** → Add complementary success test
- **Missing failure branch** → Add complementary failure test

## 3. DUAL PATH TEMPLATE SELECTION
Use: `./docs/lessons/templates/catch-block-coverage.template.ts` (Branch Coverage section)

## 4. BRANCH COVERAGE AI PROMPT TEMPLATE
"Line coverage: 100% ✅
Branch coverage: [X%] ❌
Missing branches in lines: [LINES]
Apply dual path patterns from ./docs/lessons/testing-patterns/jest-mocking-patterns.md
Create complementary tests: if failure exists add success, if success exists add failure
Target: 100% coverage across all dimensions in 20-25 minutes"

## 5. COMPLETE COVERAGE VERIFICATION
✅ Statement: 100%
✅ Branch: 100%
✅ Function: 100%
✅ Line: 100%
✅ All tests passing
✅ Time under 45 minutes total (line + branch)
✅ Pattern system proven effective
